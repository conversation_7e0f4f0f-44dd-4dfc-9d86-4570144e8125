-- Initialize databases for Dify development environment
-- This script creates the necessary databases for both the main application and plugin daemon

-- Create the main dify database (if it doesn't exist)
SELECT 'CREATE DATABASE dify'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'dify')\gexec

-- Create the plugin database (if it doesn't exist)
SELECT 'CREATE DATABASE dify_plugin'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'dify_plugin')\gexec

-- Grant permissions to the postgres user for both databases
GRANT ALL PRIVILEGES ON DATABASE dify TO postgres;
GRANT ALL PRIVILEGES ON DATABASE dify_plugin TO postgres;
