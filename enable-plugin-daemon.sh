#!/bin/bash

# Script to enable the plugin daemon in the development environment
# This script uncomments the plugin_daemon service in docker-compose.dev.yaml

echo "Enabling plugin daemon in docker-compose.dev.yaml..."

# Check if docker-compose.dev.yaml exists
if [ ! -f "docker-compose.dev.yaml" ]; then
    echo "Error: docker-compose.dev.yaml not found!"
    exit 1
fi

# Create a backup
cp docker-compose.dev.yaml docker-compose.dev.yaml.backup

# Uncomment the plugin_daemon service
sed -i 's/^  # plugin_daemon:/  plugin_daemon:/' docker-compose.dev.yaml
sed -i 's/^  #   /  /' docker-compose.dev.yaml

echo "Plugin daemon enabled in docker-compose.dev.yaml"
echo "Backup created as docker-compose.dev.yaml.backup"
echo ""
echo "To apply changes, run:"
echo "  docker compose -f docker-compose.dev.yaml down"
echo "  docker compose -f docker-compose.dev.yaml up -d"
