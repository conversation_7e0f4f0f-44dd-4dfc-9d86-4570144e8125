# Development Docker Compose Configuration
# This setup is optimized for local development with hot-reloading and debugging capabilities

x-shared-env: &shared-api-worker-env
  # Basic Configuration
  LOG_LEVEL: ${LOG_LEVEL:-DEBUG}
  DEBUG: ${DEBUG:-true}
  FLASK_DEBUG: ${FLASK_DEBUG:-true}
  DEPLOY_ENV: ${DEPLOY_ENV:-DEVELOPMENT}

  # URLs - Local Development
  CONSOLE_API_URL: ${CONSOLE_API_URL:-http://localhost:5001}
  CONSOLE_WEB_URL: ${CONSOLE_WEB_URL:-http://localhost:3000}
  SERVICE_API_URL: ${SERVICE_API_URL:-http://localhost:5001}
  APP_API_URL: ${APP_API_URL:-http://localhost:5001}
  APP_WEB_URL: ${APP_WEB_URL:-http://localhost:3000}
  FILES_URL: ${FILES_URL:-http://localhost:5001}

  # Security
  SECRET_KEY: ${SECRET_KEY:-dev-secret-key-change-in-production}

  # Database Configuration
  DB_USERNAME: ${DB_USERNAME:-postgres}
  DB_PASSWORD: ${DB_PASSWORD:-difyai123456}
  DB_HOST: ${DB_HOST:-db}
  DB_PORT: ${DB_PORT:-5432}
  DB_DATABASE: ${DB_DATABASE:-dify}

  # Redis Configuration
  REDIS_HOST: ${REDIS_HOST:-redis}
  REDIS_PORT: ${REDIS_PORT:-6379}
  REDIS_PASSWORD: ${REDIS_PASSWORD:-difyai123456}
  REDIS_DB: ${REDIS_DB:-0}

  # Celery Configuration
  CELERY_BROKER_URL: ${CELERY_BROKER_URL:-redis://:difyai123456@redis:6379/1}

  # Storage Configuration
  STORAGE_TYPE: ${STORAGE_TYPE:-opendal}
  OPENDAL_SCHEME: ${OPENDAL_SCHEME:-fs}
  OPENDAL_FS_ROOT: ${OPENDAL_FS_ROOT:-storage}

  # Vector Store Configuration
  VECTOR_STORE: ${VECTOR_STORE:-weaviate}
  WEAVIATE_ENDPOINT: ${WEAVIATE_ENDPOINT:-http://weaviate:8080}
  WEAVIATE_API_KEY: ${WEAVIATE_API_KEY:-WVF5YThaHlkYwhGUSmCRgsX3tD5ngdN8pkih}

  # Code Execution
  CODE_EXECUTION_ENDPOINT: ${CODE_EXECUTION_ENDPOINT:-http://sandbox:8194}
  CODE_EXECUTION_API_KEY: ${CODE_EXECUTION_API_KEY:-dify-sandbox}

  # CORS - Permissive for development
  WEB_API_CORS_ALLOW_ORIGINS: ${WEB_API_CORS_ALLOW_ORIGINS:-*}
  CONSOLE_CORS_ALLOW_ORIGINS: ${CONSOLE_CORS_ALLOW_ORIGINS:-*}

  # Migration Settings
  MIGRATION_ENABLED: ${MIGRATION_ENABLED:-true}

services:
  # API service - Development mode with hot reload
  api:
    build:
      context: ./api
      dockerfile: Dockerfile.dev
      target: development
    restart: unless-stopped
    environment:
      <<: *shared-api-worker-env
      MODE: api
      DIFY_BIND_ADDRESS: 0.0.0.0
      DIFY_PORT: 5001
    ports:
      - "5001:5001"
    volumes:
      # Mount source code for hot reload
      - ./api:/app/api
      - ./api/storage:/app/api/storage
      # Exclude node_modules and other build artifacts
      - /app/api/.venv
      - /app/api/__pycache__
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - dify-dev

  # Worker service - Development mode
  worker:
    build:
      context: ./api
      dockerfile: Dockerfile.dev
      target: development
    restart: unless-stopped
    environment:
      <<: *shared-api-worker-env
      MODE: worker
    volumes:
      # Mount source code for hot reload
      - ./api:/app/api
      - ./api/storage:/app/api/storage
      # Exclude node_modules and other build artifacts
      - /app/api/.venv
      - /app/api/__pycache__
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - dify-dev

  # Web service - Development mode with hot reload
  web:
    build:
      context: ./web
      dockerfile: Dockerfile.dev
      target: development
    restart: unless-stopped
    environment:
      NODE_ENV: development
      NEXT_PUBLIC_DEPLOY_ENV: ${DEPLOY_ENV:-DEVELOPMENT}
      NEXT_PUBLIC_EDITION: SELF_HOSTED
      NEXT_PUBLIC_API_PREFIX: ${CONSOLE_API_URL:-http://localhost:5001}/console/api
      NEXT_PUBLIC_PUBLIC_API_PREFIX: ${APP_API_URL:-http://localhost:5001}/api
      NEXT_TELEMETRY_DISABLED: 1
      PORT: 3000
    ports:
      - "3000:3000"
    volumes:
      # Mount source code for hot reload
      - ./web:/app/web
      - /app/web/node_modules
      - /app/web/.next
    networks:
      - dify-dev

  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${DB_USERNAME:-postgres}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-difyai123456}
      POSTGRES_DB: ${DB_DATABASE:-dify}
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USERNAME:-postgres} -d ${DB_DATABASE:-dify}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - dify-dev

  # Redis Cache
  redis:
    image: redis:6-alpine
    restart: unless-stopped
    environment:
      REDISCLI_AUTH: ${REDIS_PASSWORD:-difyai123456}
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --requirepass ${REDIS_PASSWORD:-difyai123456}
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - dify-dev

  # Weaviate Vector Database
  weaviate:
    image: semitechnologies/weaviate:1.19.0
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      PERSISTENCE_DATA_PATH: /var/lib/weaviate
      QUERY_DEFAULTS_LIMIT: 25
      AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED: false
      DEFAULT_VECTORIZER_MODULE: none
      CLUSTER_HOSTNAME: node1
      AUTHENTICATION_APIKEY_ENABLED: true
      AUTHENTICATION_APIKEY_ALLOWED_KEYS: ${WEAVIATE_API_KEY:-WVF5YThaHlkYwhGUSmCRgsX3tD5ngdN8pkih}
      AUTHENTICATION_APIKEY_USERS: <EMAIL>
      AUTHORIZATION_ADMINLIST_ENABLED: true
      AUTHORIZATION_ADMINLIST_USERS: <EMAIL>
    volumes:
      - weaviate_data:/var/lib/weaviate
    networks:
      - dify-dev

  # Sandbox for code execution
  sandbox:
    image: langgenius/dify-sandbox:0.2.12
    restart: unless-stopped
    environment:
      API_KEY: ${CODE_EXECUTION_API_KEY:-dify-sandbox}
      GIN_MODE: debug
      WORKER_TIMEOUT: 15
      ENABLE_NETWORK: true
      SANDBOX_PORT: 8194
    ports:
      - "8194:8194"
    volumes:
      - sandbox_dependencies:/dependencies
      - sandbox_conf:/conf
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8194/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - dify-dev

networks:
  dify-dev:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
  weaviate_data:
  sandbox_dependencies:
  sandbox_conf:
