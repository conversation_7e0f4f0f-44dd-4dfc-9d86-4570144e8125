# Dify - AI Application Development Platform

![Dify](https://img.shields.io/badge/Dify-v1.4.0-blue)
![Python](https://img.shields.io/badge/Python-3.11+-green)
![Next.js](https://img.shields.io/badge/Next.js-15.2.3-black)
![Docker](https://img.shields.io/badge/Docker-Compose-blue)

Dify is an open-source LLM app development platform that combines the concepts of Backend-as-a-Service and LLMOps. It enables developers to quickly build production-ready generative AI applications.

## 🚀 Features

- **Visual Workflow Builder**: Create complex AI workflows with an intuitive drag-and-drop interface
- **Multi-Model Support**: Integrate with 100+ LLMs from OpenAI, Anthropic, Google, and more
- **RAG Engine**: Built-in Retrieval-Augmented Generation with vector database support
- **Agent Framework**: Build autonomous AI agents with tool calling capabilities
- **Plugin System**: Extend functionality with custom plugins and integrations
- **Enterprise Ready**: Role-based access control, audit logs, and SSO support
- **API-First**: RESTful APIs for seamless integration with existing systems

## 🏗️ Architecture

This deployment includes:

- **Frontend**: Next.js 15 React application with TypeScript
- **Backend API**: Python Flask application with Celery workers
- **Database**: PostgreSQL 15 with vector extensions
- **Cache**: Redis for session and task management
- **Vector Store**: Weaviate for embeddings and semantic search
- **Plugin System**: Extensible plugin daemon for custom functionality
- **Reverse Proxy**: Nginx for load balancing and SSL termination

## 📋 Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- 4GB+ RAM
- 20GB+ disk space

## 🚀 Quick Start

### Production Deployment

1. **Clone the repository**
   ```bash
   git clone https://gitlab.ikatec.cloud/ai/dify.git
   cd dify
   ```

2. **Start the services**
   ```bash
   cd docker
   docker-compose up -d
   ```

3. **Access the application**
   - Web Interface: http://localhost
   - API Documentation: http://localhost/console/api/docs

### Development Setup

For developers who want to contribute or customize Dify:

1. **Clone the repository**
   ```bash
   git clone https://gitlab.ikatec.cloud/ai/dify.git
   cd dify
   ```

2. **Start development environment**
   ```bash
   docker-compose -f docker-compose.dev.yaml up -d
   ```

3. **Access development services**
   - Frontend (Next.js): http://localhost:3000
   - Backend API: http://localhost:5001
   - Database: localhost:5432
   - Redis: localhost:6379
   - Weaviate: http://localhost:8080
   - Sandbox: http://localhost:8194

4. **Development Features**
   - ✅ Hot reload for both frontend and backend
   - ✅ Debug mode enabled
   - ✅ Source code mounted as volumes
   - ✅ Direct database access for debugging
   - ✅ All ports exposed for development tools

### Initial Setup

- Create your admin account on first visit
- Configure your preferred LLM providers
- Start building your AI applications

## 🔧 Configuration

### Environment Variables

Key configuration options can be set in `docker/.env`:

```bash
# Database
DB_USERNAME=postgres
DB_PASSWORD=difyai123456
DB_DATABASE=dify

# Redis
REDIS_PASSWORD=difyai123456

# API Keys (configure your LLM providers)
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key

# Security
SECRET_KEY=your_secret_key
```

### Vector Database Options

Choose your preferred vector database by setting the `VECTOR_STORE` environment variable:

- `weaviate` (default)
- `qdrant`
- `milvus`
- `pgvector`
- `chroma`

## 🛠️ Development

### Docker Development Environment (Recommended)

The easiest way to start developing is using the development Docker Compose setup:

```bash
# Start all development services
docker-compose -f docker-compose.dev.yaml up -d

# View logs
docker-compose -f docker-compose.dev.yaml logs -f

# Stop services
docker-compose -f docker-compose.dev.yaml down
```

**Development Environment Features:**
- **Hot Reload**: Changes to source code automatically reload the application
- **Debug Mode**: Detailed logging and error messages
- **Port Exposure**: All services accessible on localhost
- **Volume Mounts**: Source code mounted for real-time editing
- **Database Access**: Direct PostgreSQL access on port 5432

### Manual Local Development Setup

If you prefer to run services individually:

1. **Prerequisites**
   ```bash
   # Install Node.js 22+ and Python 3.11+
   node --version  # Should be 22+
   python --version  # Should be 3.11+
   ```

2. **Database Setup**
   ```bash
   # Start only database services
   docker-compose -f docker-compose.dev.yaml up -d db redis weaviate
   ```

3. **API Development**
   ```bash
   cd api
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt

   # Set environment variables
   export FLASK_DEBUG=true
   export DB_HOST=localhost
   export REDIS_HOST=localhost
   export WEAVIATE_ENDPOINT=http://localhost:8080

   # Run API server
   flask run --debug --host=0.0.0.0 --port=5001
   ```

4. **Frontend Development**
   ```bash
   cd web
   pnpm install

   # Set environment variables
   export NEXT_PUBLIC_API_PREFIX=http://localhost:5001/console/api
   export NEXT_PUBLIC_PUBLIC_API_PREFIX=http://localhost:5001/api

   # Run development server
   pnpm dev
   ```

### Development Workflow

1. **Make Changes**: Edit source code in `./api` or `./web` directories
2. **Hot Reload**: Changes automatically reflect in running containers
3. **Debug**: Use browser dev tools or attach debugger to API
4. **Test**: Run tests before committing changes

### Running Tests

```bash
# API Tests
cd api
python -m pytest

# Frontend Tests
cd web
pnpm test

# Run tests in Docker
docker-compose -f docker-compose.dev.yaml exec api python -m pytest
docker-compose -f docker-compose.dev.yaml exec web pnpm test
```

### Development Database Access

```bash
# Connect to PostgreSQL
docker-compose -f docker-compose.dev.yaml exec db psql -U postgres -d dify

# Connect to Redis
docker-compose -f docker-compose.dev.yaml exec redis redis-cli -a difyai123456

# Access Weaviate
curl http://localhost:8080/v1/meta
```

## 📚 Documentation

- [Official Documentation](https://docs.dify.ai)
- [API Reference](https://docs.dify.ai/api-reference)
- [Plugin Development Guide](https://docs.dify.ai/plugins)
- [Deployment Guide](https://docs.dify.ai/deployment)

## 🔌 Plugin System

This installation includes the plugin daemon for extending Dify's capabilities:

- **Plugin Management**: Install and manage plugins through the web interface
- **Custom Tools**: Create custom tools and integrations
- **Model Providers**: Add support for additional LLM providers
- **Data Sources**: Connect to external data sources

## 🚨 Troubleshooting

### Development Environment Issues

1. **Services won't start**: Check if ports are already in use
   ```bash
   # Check what's using the ports
   lsof -i :3000  # Frontend
   lsof -i :5001  # API
   lsof -i :5432  # Database

   # Stop conflicting services or change ports in docker-compose.dev.yaml
   ```

2. **Hot reload not working**: Ensure volumes are properly mounted
   ```bash
   # Restart with fresh volumes
   docker-compose -f docker-compose.dev.yaml down -v
   docker-compose -f docker-compose.dev.yaml up -d
   ```

3. **Database connection issues**: Reset database
   ```bash
   # Reset database and start fresh
   docker-compose -f docker-compose.dev.yaml down -v
   docker volume rm dify_postgres_data
   docker-compose -f docker-compose.dev.yaml up -d
   ```

4. **Frontend build errors**: Clear Next.js cache
   ```bash
   # Clear Next.js cache
   docker-compose -f docker-compose.dev.yaml exec web rm -rf .next
   docker-compose -f docker-compose.dev.yaml restart web
   ```

### Production Environment Issues

1. **Plugin Daemon Errors**: If you see plugin-related errors, ensure the plugin daemon is running:
   ```bash
   docker-compose logs plugin_daemon
   ```

2. **Database Connection Issues**: Check database health:
   ```bash
   docker-compose exec db pg_isready
   ```

3. **Memory Issues**: Ensure you have sufficient RAM allocated to Docker

### Development Commands Cheat Sheet

```bash
# Start development environment
docker-compose -f docker-compose.dev.yaml up -d

# View logs for all services
docker-compose -f docker-compose.dev.yaml logs -f

# View logs for specific service
docker-compose -f docker-compose.dev.yaml logs -f api

# Restart a specific service
docker-compose -f docker-compose.dev.yaml restart web

# Execute commands in containers
docker-compose -f docker-compose.dev.yaml exec api bash
docker-compose -f docker-compose.dev.yaml exec web sh

# Clean up everything
docker-compose -f docker-compose.dev.yaml down -v
docker system prune -f
```

### Recent Fixes

- ✅ Fixed frontend error handling for plugin daemon connectivity issues
- ✅ Improved graceful degradation when plugin services are unavailable
- ✅ Added comprehensive development environment setup

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the Apache License 2.0 - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built on the open-source [Dify](https://github.com/langgenius/dify) platform
- Powered by the amazing open-source AI community
- Special thanks to all contributors and maintainers

## 📞 Support

- 📧 Email: [Your support email]
- 💬 Discord: [Your Discord server]
- 🐛 Issues: [GitLab Issues](https://gitlab.ikatec.cloud/ai/dify/-/issues)
- 📖 Documentation: [docs.dify.ai](https://docs.dify.ai)

---

**Made with ❤️ for the AI community**
