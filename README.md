# Dify - AI Application Development Platform

![Dify](https://img.shields.io/badge/Dify-v1.4.0-blue)
![Python](https://img.shields.io/badge/Python-3.11+-green)
![Next.js](https://img.shields.io/badge/Next.js-15.2.3-black)
![Docker](https://img.shields.io/badge/Docker-Compose-blue)

Dify is an open-source LLM app development platform that combines the concepts of Backend-as-a-Service and LLMOps. It enables developers to quickly build production-ready generative AI applications.

## 🚀 Features

- **Visual Workflow Builder**: Create complex AI workflows with an intuitive drag-and-drop interface
- **Multi-Model Support**: Integrate with 100+ LLMs from OpenAI, Anthropic, Google, and more
- **RAG Engine**: Built-in Retrieval-Augmented Generation with vector database support
- **Agent Framework**: Build autonomous AI agents with tool calling capabilities
- **Plugin System**: Extend functionality with custom plugins and integrations
- **Enterprise Ready**: Role-based access control, audit logs, and SSO support
- **API-First**: RESTful APIs for seamless integration with existing systems

## 🏗️ Architecture

This deployment includes:

- **Frontend**: Next.js 15 React application with TypeScript
- **Backend API**: Python Flask application with Celery workers
- **Database**: PostgreSQL 15 with vector extensions
- **Cache**: Redis for session and task management
- **Vector Store**: Weaviate for embeddings and semantic search
- **Plugin System**: Extensible plugin daemon for custom functionality
- **Reverse Proxy**: Nginx for load balancing and SSL termination

## 📋 Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- 4GB+ RAM
- 20GB+ disk space

## 🚀 Quick Start

1. **Clone the repository**
   ```bash
   git clone https://gitlab.ikatec.cloud/ai/dify.git
   cd dify
   ```

2. **Start the services**
   ```bash
   cd docker
   docker-compose up -d
   ```

3. **Access the application**
   - Web Interface: http://localhost
   - API Documentation: http://localhost/console/api/docs

4. **Initial Setup**
   - Create your admin account on first visit
   - Configure your preferred LLM providers
   - Start building your AI applications

## 🔧 Configuration

### Environment Variables

Key configuration options can be set in `docker/.env`:

```bash
# Database
DB_USERNAME=postgres
DB_PASSWORD=difyai123456
DB_DATABASE=dify

# Redis
REDIS_PASSWORD=difyai123456

# API Keys (configure your LLM providers)
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key

# Security
SECRET_KEY=your_secret_key
```

### Vector Database Options

Choose your preferred vector database by setting the `VECTOR_STORE` environment variable:

- `weaviate` (default)
- `qdrant`
- `milvus`
- `pgvector`
- `chroma`

## 🛠️ Development

### Local Development Setup

1. **API Development**
   ```bash
   cd api
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   flask run --debug
   ```

2. **Frontend Development**
   ```bash
   cd web
   pnpm install
   pnpm dev
   ```

### Running Tests

```bash
# API Tests
cd api
pytest

# Frontend Tests
cd web
pnpm test
```

## 📚 Documentation

- [Official Documentation](https://docs.dify.ai)
- [API Reference](https://docs.dify.ai/api-reference)
- [Plugin Development Guide](https://docs.dify.ai/plugins)
- [Deployment Guide](https://docs.dify.ai/deployment)

## 🔌 Plugin System

This installation includes the plugin daemon for extending Dify's capabilities:

- **Plugin Management**: Install and manage plugins through the web interface
- **Custom Tools**: Create custom tools and integrations
- **Model Providers**: Add support for additional LLM providers
- **Data Sources**: Connect to external data sources

## 🚨 Troubleshooting

### Common Issues

1. **Plugin Daemon Errors**: If you see plugin-related errors, ensure the plugin daemon is running:
   ```bash
   docker-compose logs plugin_daemon
   ```

2. **Database Connection Issues**: Check database health:
   ```bash
   docker-compose exec db pg_isready
   ```

3. **Memory Issues**: Ensure you have sufficient RAM allocated to Docker

### Recent Fixes

- ✅ Fixed frontend error handling for plugin daemon connectivity issues
- ✅ Improved graceful degradation when plugin services are unavailable

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the Apache License 2.0 - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built on the open-source [Dify](https://github.com/langgenius/dify) platform
- Powered by the amazing open-source AI community
- Special thanks to all contributors and maintainers

## 📞 Support

- 📧 Email: [Your support email]
- 💬 Discord: [Your Discord server]
- 🐛 Issues: [GitLab Issues](https://gitlab.ikatec.cloud/ai/dify/-/issues)
- 📖 Documentation: [docs.dify.ai](https://docs.dify.ai)

---

**Made with ❤️ for the AI community**
