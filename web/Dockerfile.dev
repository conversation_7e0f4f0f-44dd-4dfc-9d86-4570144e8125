# Development Dockerfile for Web service
FROM node:22-alpine3.21 AS base

# Install system dependencies
RUN apk add --no-cache tzdata git

# Install pnpm
RUN npm install -g pnpm@10.8.0
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

# Development stage
FROM base AS development

WORKDIR /app/web

# Set environment variables for development
ENV NODE_ENV=development
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000

# Set timezone
ENV TZ=UTC
RUN ln -s /usr/share/zoneinfo/${TZ} /etc/localtime \
    && echo ${TZ} > /etc/timezone

# Copy package files
COPY package.json pnpm-lock.yaml ./

# Install dependencies (including dev dependencies)
RUN pnpm install --frozen-lockfile

# Copy source code (this will be overridden by volume mount in development)
COPY . .

# Expose port
EXPOSE 3000

# Default command for development
CMD ["pnpm", "dev"]
