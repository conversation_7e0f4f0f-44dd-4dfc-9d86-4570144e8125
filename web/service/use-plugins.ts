import useSWR from 'swr'
import { useCallback } from 'react'
import { get, post } from './base'
import type { Plugin } from '@/app/components/plugins/types'

// Types for plugin-related API responses
export type PluginListResponse = {
  plugins: Plugin[]
}

export type CheckInstalledResponse = {
  plugins: Plugin[]
}

export type CheckInstalledParams = {
  pluginIds: string[]
  enabled: boolean
}

export type CheckDependenciesResponse = {
  leaked_dependencies: string[]
}

export type MarketplacePluginsResponse = {
  plugins: Plugin[]
}

export type InstallPluginResponse = {
  task_id: string
}

export type UpdatePluginResponse = {
  task_id: string
}

export type PluginVersionListResponse = {
  versions: string[]
}

export type DownloadPluginResponse = {
  task_id: string
}

export type PermissionsResponse = {
  permissions: any[]
}

export type MutationPermissionsResponse = {
  success: boolean
}

// Hook to get the list of installed plugins
export const useInstalledPluginList = () => {
  const { data, error, mutate } = useSWR<PluginListResponse>(
    '/workspaces/current/plugin/list',
    get,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      errorRetryCount: 3,
      onError: (error) => {
        // Graceful error handling - return empty list if plugin daemon is unavailable
        console.warn('Plugin daemon unavailable:', error)
      },
    }
  )

  return {
    data: data || { plugins: [] }, // Return empty array if no data
    error,
    mutate,
    isLoading: !error && !data,
  }
}

// Hook to check if specific plugins are installed
export const useCheckInstalled = ({ pluginIds, enabled }: CheckInstalledParams) => {
  const { data, error, mutate } = useSWR<CheckInstalledResponse>(
    enabled && pluginIds.length > 0
      ? ['/workspaces/current/plugin/list/installations/ids', pluginIds]
      : null,
    async ([url, ids]) => {
      try {
        return await get<CheckInstalledResponse>(`${url}?plugin_ids=${ids.join(',')}`)
      } catch (error) {
        // Graceful error handling - return empty list if plugin daemon is unavailable
        console.warn('Plugin daemon unavailable for check installed:', error)
        return { plugins: [] }
      }
    },
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      errorRetryCount: 3,
    }
  )

  return {
    data: data || { plugins: [] }, // Return empty array if no data
    error,
    mutate,
    isLoading: enabled && pluginIds.length > 0 && !error && !data,
  }
}

// Hook to invalidate (refresh) the installed plugin list
export const useInvalidateInstalledPluginList = () => {
  const { mutate } = useSWR('/workspaces/current/plugin/list', null, {
    revalidateOnMount: false,
  })

  return useCallback(() => {
    try {
      mutate()
    } catch (error) {
      // Graceful error handling
      console.warn('Failed to invalidate plugin list:', error)
    }
  }, [mutate])
}

// Hook to get plugin tasks (installation/upgrade tasks)
export const usePluginTaskList = () => {
  const { data, error, mutate } = useSWR(
    '/workspaces/current/plugin/tasks?page=1&page_size=255',
    get,
    {
      refreshInterval: 2000, // Poll every 2 seconds for task updates
      revalidateOnFocus: false,
      errorRetryCount: 3,
      onError: (error) => {
        // Graceful error handling
        console.warn('Plugin daemon unavailable for tasks:', error)
      },
    }
  )

  return {
    data: data || { tasks: [] }, // Return empty array if no data
    error,
    mutate,
    isLoading: !error && !data,
  }
}

// Hook to get plugin debugging key
export const usePluginDebuggingKey = () => {
  const { data, error, mutate } = useSWR(
    '/workspaces/current/plugin/debugging-key',
    get,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      errorRetryCount: 3,
      onError: (error) => {
        // Graceful error handling
        console.warn('Plugin daemon unavailable for debugging key:', error)
      },
    }
  )

  return {
    data,
    error,
    mutate,
    isLoading: !error && !data,
  }
}

// Hook to refresh plugin-related data after installation/uninstallation
export const useRefreshPluginData = () => {
  const invalidateInstalledPluginList = useInvalidateInstalledPluginList()

  return useCallback(() => {
    try {
      invalidateInstalledPluginList()
    } catch (error) {
      console.warn('Failed to refresh plugin data:', error)
    }
  }, [invalidateInstalledPluginList])
}

// Hook to check plugin dependencies for an app
export const useMutationCheckDependencies = () => {
  const mutateAsync = useCallback(async (appId: string): Promise<CheckDependenciesResponse> => {
    try {
      const response = await post<CheckDependenciesResponse>(`/apps/${appId}/plugin-dependencies/check`)
      return response || { leaked_dependencies: [] }
    } catch (error) {
      // Graceful error handling - return empty dependencies if plugin daemon is unavailable
      console.warn('Plugin daemon unavailable for dependency check:', error)
      return { leaked_dependencies: [] }
    }
  }, [])

  return {
    mutateAsync,
  }
}

// Hook to fetch plugins from marketplace by IDs
export const useFetchPluginsInMarketPlaceByIds = (pluginIds: string[], options: { retry?: boolean } = {}) => {
  const { retry = true } = options
  const enabled = pluginIds.length > 0

  const { data, error, mutate, isLoading } = useSWR<MarketplacePluginsResponse>(
    enabled ? ['/marketplace/plugins/by-ids', pluginIds] : null,
    async ([url, ids]) => {
      try {
        return await get<MarketplacePluginsResponse>(`${url}?plugin_ids=${ids.join(',')}`)
      } catch (error) {
        // Graceful error handling - return empty list if marketplace is unavailable
        console.warn('Marketplace unavailable for plugin fetch:', error)
        return { plugins: [] }
      }
    },
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      errorRetryCount: retry ? 3 : 0,
    }
  )

  return {
    data: data || { plugins: [] }, // Return empty array if no data
    error,
    mutate,
    isLoading: enabled && !error && !data,
    refetch: mutate,
  }
}

// Hook to install a plugin from marketplace
export const useInstallPackageFromMarketPlace = () => {
  const mutateAsync = useCallback(async (pluginId: string): Promise<InstallPluginResponse> => {
    try {
      const response = await post<InstallPluginResponse>(`/workspaces/current/plugin/install`, {
        body: { plugin_id: pluginId }
      })
      return response || { task_id: '' }
    } catch (error) {
      // Graceful error handling - return empty task_id if plugin daemon is unavailable
      console.warn('Plugin daemon unavailable for installation:', error)
      return { task_id: '' }
    }
  }, [])

  return {
    mutateAsync,
  }
}

// Hook to update a plugin from marketplace
export const useUpdatePackageFromMarketPlace = () => {
  const mutateAsync = useCallback(async (pluginId: string): Promise<UpdatePluginResponse> => {
    try {
      const response = await post<UpdatePluginResponse>(`/workspaces/current/plugin/upgrade`, {
        body: { plugin_id: pluginId }
      })
      return response || { task_id: '' }
    } catch (error) {
      // Graceful error handling - return empty task_id if plugin daemon is unavailable
      console.warn('Plugin daemon unavailable for update:', error)
      return { task_id: '' }
    }
  }, [])

  return {
    mutateAsync,
  }
}

// Hook to get version list of a plugin
export const useVersionListOfPlugin = (pluginId: string, enabled: boolean = true) => {
  const { data, error, mutate } = useSWR<PluginVersionListResponse>(
    enabled && pluginId ? `/marketplace/plugins/${pluginId}/versions` : null,
    get,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      errorRetryCount: 3,
      onError: (error: any) => {
        // Graceful error handling
        console.warn('Marketplace unavailable for version list:', error)
      },
    }
  )

  return {
    data: data || { versions: [] }, // Return empty array if no data
    error,
    mutate,
    isLoading: enabled && pluginId && !error && !data,
  }
}

// Hook to download a plugin
export const useDownloadPlugin = () => {
  const mutateAsync = useCallback(async (pluginId: string, version?: string): Promise<DownloadPluginResponse> => {
    try {
      const response = await post<DownloadPluginResponse>(`/workspaces/current/plugin/download`, {
        body: { plugin_id: pluginId, version }
      })
      return response || { task_id: '' }
    } catch (error) {
      // Graceful error handling - return empty task_id if plugin daemon is unavailable
      console.warn('Plugin daemon unavailable for download:', error)
      return { task_id: '' }
    }
  }, [])

  return {
    mutateAsync,
  }
}

// Hook to invalidate permissions
export const useInvalidatePermissions = () => {
  const { mutate } = useSWR('/workspaces/current/plugin/permissions', null, {
    revalidateOnMount: false,
  })

  return useCallback(() => {
    try {
      mutate()
    } catch (error) {
      // Graceful error handling
      console.warn('Failed to invalidate permissions:', error)
    }
  }, [mutate])
}

// Hook to mutate permissions
export const useMutationPermissions = () => {
  const mutateAsync = useCallback(async (permissions: any): Promise<MutationPermissionsResponse> => {
    try {
      const response = await post<MutationPermissionsResponse>(`/workspaces/current/plugin/permissions`, {
        body: permissions
      })
      return response || { success: false }
    } catch (error) {
      // Graceful error handling - return failure if plugin daemon is unavailable
      console.warn('Plugin daemon unavailable for permissions mutation:', error)
      return { success: false }
    }
  }, [])

  return {
    mutateAsync,
  }
}

// Export all hooks for easy importing
export {
  useInstalledPluginList as default,
}
