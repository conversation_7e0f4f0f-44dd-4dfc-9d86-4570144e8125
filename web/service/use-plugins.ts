import useSWR from 'swr'
import { useCallback } from 'react'
import { get, post } from './base'
import type { Plugin } from '@/app/components/plugins/types'

// Types for plugin-related API responses
export type PluginListResponse = {
  plugins: Plugin[]
}

export type CheckInstalledResponse = {
  plugins: Plugin[]
}

export type CheckInstalledParams = {
  pluginIds: string[]
  enabled: boolean
}

export type CheckDependenciesResponse = {
  leaked_dependencies: string[]
}

// Hook to get the list of installed plugins
export const useInstalledPluginList = () => {
  const { data, error, mutate } = useSWR<PluginListResponse>(
    '/workspaces/current/plugin/list',
    get,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      errorRetryCount: 3,
      onError: (error) => {
        // Graceful error handling - return empty list if plugin daemon is unavailable
        console.warn('Plugin daemon unavailable:', error)
      },
    }
  )

  return {
    data: data || { plugins: [] }, // Return empty array if no data
    error,
    mutate,
    isLoading: !error && !data,
  }
}

// Hook to check if specific plugins are installed
export const useCheckInstalled = ({ pluginIds, enabled }: CheckInstalledParams) => {
  const { data, error, mutate } = useSWR<CheckInstalledResponse>(
    enabled && pluginIds.length > 0
      ? ['/workspaces/current/plugin/list/installations/ids', pluginIds]
      : null,
    async ([url, ids]) => {
      try {
        return await get<CheckInstalledResponse>(`${url}?plugin_ids=${ids.join(',')}`)
      } catch (error) {
        // Graceful error handling - return empty list if plugin daemon is unavailable
        console.warn('Plugin daemon unavailable for check installed:', error)
        return { plugins: [] }
      }
    },
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      errorRetryCount: 3,
    }
  )

  return {
    data: data || { plugins: [] }, // Return empty array if no data
    error,
    mutate,
    isLoading: enabled && pluginIds.length > 0 && !error && !data,
  }
}

// Hook to invalidate (refresh) the installed plugin list
export const useInvalidateInstalledPluginList = () => {
  const { mutate } = useSWR('/workspaces/current/plugin/list', null, {
    revalidateOnMount: false,
  })

  return useCallback(() => {
    try {
      mutate()
    } catch (error) {
      // Graceful error handling
      console.warn('Failed to invalidate plugin list:', error)
    }
  }, [mutate])
}

// Hook to get plugin tasks (installation/upgrade tasks)
export const usePluginTaskList = () => {
  const { data, error, mutate } = useSWR(
    '/workspaces/current/plugin/tasks?page=1&page_size=255',
    get,
    {
      refreshInterval: 2000, // Poll every 2 seconds for task updates
      revalidateOnFocus: false,
      errorRetryCount: 3,
      onError: (error) => {
        // Graceful error handling
        console.warn('Plugin daemon unavailable for tasks:', error)
      },
    }
  )

  return {
    data: data || { tasks: [] }, // Return empty array if no data
    error,
    mutate,
    isLoading: !error && !data,
  }
}

// Hook to get plugin debugging key
export const usePluginDebuggingKey = () => {
  const { data, error, mutate } = useSWR(
    '/workspaces/current/plugin/debugging-key',
    get,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      errorRetryCount: 3,
      onError: (error) => {
        // Graceful error handling
        console.warn('Plugin daemon unavailable for debugging key:', error)
      },
    }
  )

  return {
    data,
    error,
    mutate,
    isLoading: !error && !data,
  }
}

// Hook to refresh plugin-related data after installation/uninstallation
export const useRefreshPluginData = () => {
  const invalidateInstalledPluginList = useInvalidateInstalledPluginList()

  return useCallback(() => {
    try {
      invalidateInstalledPluginList()
    } catch (error) {
      console.warn('Failed to refresh plugin data:', error)
    }
  }, [invalidateInstalledPluginList])
}

// Hook to check plugin dependencies for an app
export const useMutationCheckDependencies = () => {
  const mutateAsync = useCallback(async (appId: string): Promise<CheckDependenciesResponse> => {
    try {
      const response = await post<CheckDependenciesResponse>(`/apps/${appId}/plugin-dependencies/check`)
      return response || { leaked_dependencies: [] }
    } catch (error) {
      // Graceful error handling - return empty dependencies if plugin daemon is unavailable
      console.warn('Plugin daemon unavailable for dependency check:', error)
      return { leaked_dependencies: [] }
    }
  }, [])

  return {
    mutateAsync,
  }
}

// Export all hooks for easy importing
export {
  useInstalledPluginList as default,
}
