# Production Dockerfile for Web service
FROM node:22-alpine3.21 AS base

# Install system dependencies
RUN apk add --no-cache tzdata

# Install pnpm
RUN npm install -g pnpm@10.8.0
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

# Dependencies stage
FROM base AS dependencies

WORKDIR /app/web

# Copy package files
COPY package.json pnpm-lock.yaml ./

# Install dependencies
RUN pnpm install --frozen-lockfile

# Build stage
FROM base AS builder

WORKDIR /app/web

# Copy dependencies
COPY --from=dependencies /app/web/node_modules ./node_modules

# Copy source code
COPY . .

# Set build environment variables
ENV NODE_ENV=production
ENV NODE_OPTIONS="--max-old-space-size=4096"
ENV NEXT_TELEMETRY_DISABLED=1

# Build the application
RUN pnpm build

# Production stage
FROM base AS production

# Set environment variables for production
ENV NODE_ENV=production
ENV EDITION=SELF_HOSTED
ENV DEPLOY_ENV=PRODUCTION
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000
ENV PM2_INSTANCES=2

# Set timezone
ENV TZ=UTC
RUN ln -s /usr/share/zoneinfo/${TZ} /etc/localtime \
    && echo ${TZ} > /etc/timezone

WORKDIR /app/web

# Copy built application from builder stage
COPY --from=builder /app/web/public ./public
COPY --from=builder /app/web/.next/standalone ./
COPY --from=builder /app/web/.next/static ./.next/static

# Copy entrypoint script
COPY docker/entrypoint.sh ./entrypoint.sh
RUN chmod +x ./entrypoint.sh

# Install PM2 globally for production process management
RUN pnpm add -g pm2

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001 -G nodejs

# Create PM2 directory and set permissions
RUN mkdir -p /.pm2 && \
    chown -R nextjs:nodejs /.pm2 /app/web

# Build argument for commit SHA
ARG COMMIT_SHA
ENV COMMIT_SHA=${COMMIT_SHA}

# Switch to non-root user
USER nextjs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

ENTRYPOINT ["/bin/sh", "./entrypoint.sh"]
