# Production Docker Compose Configuration
# This setup is optimized for production deployment with security and performance in mind

x-shared-env: &shared-api-worker-env
  # Basic Configuration
  LOG_LEVEL: ${LOG_LEVEL:-INFO}
  DEBUG: ${DEBUG:-false}
  FLASK_DEBUG: ${FLASK_DEBUG:-false}
  DEPLOY_ENV: ${DEPLOY_ENV:-PRODUCTION}
  
  # URLs - Production
  CONSOLE_API_URL: ${CONSOLE_API_URL}
  CONSOLE_WEB_URL: ${CONSOLE_WEB_URL}
  SERVICE_API_URL: ${SERVICE_API_URL}
  APP_API_URL: ${APP_API_URL}
  APP_WEB_URL: ${APP_WEB_URL}
  FILES_URL: ${FILES_URL}
  
  # Security
  SECRET_KEY: ${SECRET_KEY}
  
  # Database Configuration
  DB_USERNAME: ${DB_USERNAME:-postgres}
  DB_PASSWORD: ${DB_PASSWORD}
  DB_HOST: ${DB_HOST:-db}
  DB_PORT: ${DB_PORT:-5432}
  DB_DATABASE: ${DB_DATABASE:-dify}
  
  # Redis Configuration
  REDIS_HOST: ${REDIS_HOST:-redis}
  REDIS_PORT: ${REDIS_PORT:-6379}
  REDIS_PASSWORD: ${REDIS_PASSWORD}
  REDIS_DB: ${REDIS_DB:-0}
  
  # Celery Configuration
  CELERY_BROKER_URL: ${CELERY_BROKER_URL}
  
  # Storage Configuration
  STORAGE_TYPE: ${STORAGE_TYPE:-opendal}
  OPENDAL_SCHEME: ${OPENDAL_SCHEME:-fs}
  OPENDAL_FS_ROOT: ${OPENDAL_FS_ROOT:-storage}
  
  # Vector Store Configuration
  VECTOR_STORE: ${VECTOR_STORE:-weaviate}
  WEAVIATE_ENDPOINT: ${WEAVIATE_ENDPOINT:-http://weaviate:8080}
  WEAVIATE_API_KEY: ${WEAVIATE_API_KEY}
  
  # Code Execution
  CODE_EXECUTION_ENDPOINT: ${CODE_EXECUTION_ENDPOINT:-http://sandbox:8194}
  CODE_EXECUTION_API_KEY: ${CODE_EXECUTION_API_KEY}
  
  # CORS - Restrictive for production
  WEB_API_CORS_ALLOW_ORIGINS: ${WEB_API_CORS_ALLOW_ORIGINS}
  CONSOLE_CORS_ALLOW_ORIGINS: ${CONSOLE_CORS_ALLOW_ORIGINS}
  
  # Performance Settings
  SERVER_WORKER_AMOUNT: ${SERVER_WORKER_AMOUNT:-2}
  SERVER_WORKER_CLASS: ${SERVER_WORKER_CLASS:-gevent}
  CELERY_WORKER_AMOUNT: ${CELERY_WORKER_AMOUNT:-2}

services:
  # API service - Production optimized
  api:
    image: ${REGISTRY_URL:-localhost}/dify-api:${IMAGE_TAG:-latest}
    build:
      context: ./api
      dockerfile: Dockerfile.prod
      target: production
      args:
        COMMIT_SHA: ${COMMIT_SHA:-unknown}
    restart: always
    environment:
      <<: *shared-api-worker-env
      MODE: api
      DIFY_BIND_ADDRESS: 0.0.0.0
      DIFY_PORT: 5001
    volumes:
      - app_storage:/app/api/storage
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - dify-prod
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Worker service - Production optimized
  worker:
    image: ${REGISTRY_URL:-localhost}/dify-api:${IMAGE_TAG:-latest}
    restart: always
    environment:
      <<: *shared-api-worker-env
      MODE: worker
    volumes:
      - app_storage:/app/api/storage
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - dify-prod
    deploy:
      replicas: ${WORKER_REPLICAS:-2}

  # Web service - Production optimized
  web:
    image: ${REGISTRY_URL:-localhost}/dify-web:${IMAGE_TAG:-latest}
    build:
      context: ./web
      dockerfile: Dockerfile.prod
      target: production
      args:
        COMMIT_SHA: ${COMMIT_SHA:-unknown}
    restart: always
    environment:
      NODE_ENV: production
      NEXT_PUBLIC_DEPLOY_ENV: ${DEPLOY_ENV:-PRODUCTION}
      NEXT_PUBLIC_EDITION: SELF_HOSTED
      NEXT_PUBLIC_API_PREFIX: ${CONSOLE_API_URL}/console/api
      NEXT_PUBLIC_PUBLIC_API_PREFIX: ${APP_API_URL}/api
      NEXT_TELEMETRY_DISABLED: 1
      PORT: 3000
      PM2_INSTANCES: ${PM2_INSTANCES:-2}
    networks:
      - dify-prod
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    restart: always
    environment:
      POSTGRES_USER: ${DB_USERNAME:-postgres}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: ${DB_DATABASE:-dify}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USERNAME:-postgres} -d ${DB_DATABASE:-dify}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - dify-prod
    command: >
      postgres -c 'max_connections=${POSTGRES_MAX_CONNECTIONS:-100}'
               -c 'shared_buffers=${POSTGRES_SHARED_BUFFERS:-256MB}'
               -c 'work_mem=${POSTGRES_WORK_MEM:-8MB}'
               -c 'maintenance_work_mem=${POSTGRES_MAINTENANCE_WORK_MEM:-128MB}'
               -c 'effective_cache_size=${POSTGRES_EFFECTIVE_CACHE_SIZE:-8GB}'

  # Redis Cache
  redis:
    image: redis:6-alpine
    restart: always
    environment:
      REDISCLI_AUTH: ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    command: redis-server --requirepass ${REDIS_PASSWORD} --maxmemory ${REDIS_MAX_MEMORY:-512mb} --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - dify-prod

  # Weaviate Vector Database
  weaviate:
    image: semitechnologies/weaviate:1.19.0
    restart: always
    environment:
      PERSISTENCE_DATA_PATH: /var/lib/weaviate
      QUERY_DEFAULTS_LIMIT: 25
      AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED: false
      DEFAULT_VECTORIZER_MODULE: none
      CLUSTER_HOSTNAME: node1
      AUTHENTICATION_APIKEY_ENABLED: true
      AUTHENTICATION_APIKEY_ALLOWED_KEYS: ${WEAVIATE_API_KEY}
      AUTHENTICATION_APIKEY_USERS: ${WEAVIATE_USERS:-<EMAIL>}
      AUTHORIZATION_ADMINLIST_ENABLED: true
      AUTHORIZATION_ADMINLIST_USERS: ${WEAVIATE_USERS:-<EMAIL>}
    volumes:
      - weaviate_data:/var/lib/weaviate
    networks:
      - dify-prod

  # Sandbox for code execution
  sandbox:
    image: langgenius/dify-sandbox:0.2.12
    restart: always
    environment:
      API_KEY: ${CODE_EXECUTION_API_KEY}
      GIN_MODE: release
      WORKER_TIMEOUT: 15
      ENABLE_NETWORK: ${SANDBOX_ENABLE_NETWORK:-false}
      SANDBOX_PORT: 8194
    volumes:
      - sandbox_dependencies:/dependencies
      - sandbox_conf:/conf
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8194/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - dify-prod
  plugin_daemon:
    image: langgenius/dify-plugin-daemon:0.0.10-local
    restart: unless-stopped
    environment:
      <<: *shared-api-worker-env
      DB_DATABASE: ${DB_PLUGIN_DATABASE:-dify_plugin}
      SERVER_PORT: ${PLUGIN_DAEMON_PORT:-5002}
      SERVER_KEY: ${PLUGIN_DAEMON_KEY:-lYkiYYT6owG+71oLerGzA7GXCgOT++6ovaezWAjpCjf+Sjc3ZtU+qUEi}
      MAX_PLUGIN_PACKAGE_SIZE: ${PLUGIN_MAX_PACKAGE_SIZE:-52428800}
      PPROF_ENABLED: ${PLUGIN_PPROF_ENABLED:-false}
      DIFY_INNER_API_URL: ${PLUGIN_DIFY_INNER_API_URL:-http://api:5001}
      DIFY_INNER_API_KEY: ${PLUGIN_DIFY_INNER_API_KEY:-QaHbTe77CtuXmsfyhR7+vRjI/+XbV1AaFy691iy+kGDv2Jvy0/eAh8Y1}
      PLUGIN_REMOTE_INSTALLING_HOST: ${PLUGIN_DEBUGGING_HOST:-0.0.0.0}
      PLUGIN_REMOTE_INSTALLING_PORT: ${PLUGIN_DEBUGGING_PORT:-5003}
      PLUGIN_WORKING_PATH: ${PLUGIN_WORKING_PATH:-/app/storage/cwd}
      FORCE_VERIFYING_SIGNATURE: ${FORCE_VERIFYING_SIGNATURE:-true}
      PYTHON_ENV_INIT_TIMEOUT: ${PLUGIN_PYTHON_ENV_INIT_TIMEOUT:-120}
      PLUGIN_MAX_EXECUTION_TIMEOUT: ${PLUGIN_MAX_EXECUTION_TIMEOUT:-600}
      PIP_MIRROR_URL: ${PIP_MIRROR_URL:-}
      PLUGIN_STORAGE_TYPE: ${PLUGIN_STORAGE_TYPE:-local}
      PLUGIN_STORAGE_LOCAL_ROOT: ${PLUGIN_STORAGE_LOCAL_ROOT:-/app/storage}
      PLUGIN_INSTALLED_PATH: ${PLUGIN_INSTALLED_PATH:-plugin}
      PLUGIN_PACKAGE_CACHE_PATH: ${PLUGIN_PACKAGE_CACHE_PATH:-plugin_packages}
      PLUGIN_MEDIA_CACHE_PATH: ${PLUGIN_MEDIA_CACHE_PATH:-assets}
    ports:
      - "${EXPOSE_PLUGIN_DEBUGGING_PORT:-5003}:${PLUGIN_DEBUGGING_PORT:-5003}"
      - "${EXPOSE_PLUGIN_DAEMON_PORT:-5002}:${PLUGIN_DAEMON_PORT:-5002}"
    volumes:
      - plugin_daemon_storage:/app/storage
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - dify-prod

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    restart: always
    ports:
      - "${NGINX_PORT:-80}:80"
      - "${NGINX_SSL_PORT:-443}:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - api
      - web
    networks:
      - dify-prod
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  dify-prod:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
  weaviate_data:
  sandbox_dependencies:
  sandbox_conf:
  app_storage:
  nginx_logs:
  plugin_daemon_storage:
