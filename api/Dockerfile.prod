# Production Dockerfile for API service
FROM python:3.12-slim-bookworm AS base

WORKDIR /app/api

# Install uv for faster package management
ENV UV_VERSION=0.6.14
RUN pip install --no-cache-dir uv==${UV_VERSION}

# Dependencies stage
FROM base AS dependencies

# Install build dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        gcc g++ libc-dev libffi-dev libgmp-dev libmpfr-dev libmpc-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY api/pyproject.toml api/uv.lock ./
RUN uv sync --locked --no-dev

# Production stage
FROM base AS production

# Set environment variables for production
ENV FLASK_APP=app.py
ENV EDITION=SELF_HOSTED
ENV DEPLOY_ENV=PRODUCTION
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Set timezone
ENV TZ=UTC

# Install runtime dependencies only
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        # Runtime dependencies
        curl nodejs libgmp-dev libmpfr-dev libmpc-dev \
        # For Security
        expat libldap-2.5-0 perl libsqlite3-0 zlib1g \
        # File type detection
        media-types libmagic1 \
    && apt-get autoremove -y \
    && rm -rf /var/lib/apt/lists/*

# Copy Python environment from dependencies stage
ENV VIRTUAL_ENV=/app/api/.venv
COPY --from=dependencies ${VIRTUAL_ENV} ${VIRTUAL_ENV}
ENV PATH="${VIRTUAL_ENV}/bin:${PATH}"

# Download nltk data
RUN python -c "import nltk; nltk.download('punkt'); nltk.download('averaged_perceptron_tagger')"

# Set up tiktoken cache
ENV TIKTOKEN_CACHE_DIR=/app/api/.tiktoken_cache
RUN python -c "import tiktoken; tiktoken.encoding_for_model('gpt2')"

# Create non-root user for security
RUN groupadd -r dify && useradd -r -g dify dify

# Copy source code
COPY --chown=dify:dify . /app/api/

# Copy entrypoint
COPY --chown=dify:dify api/docker/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

# Create storage directory
RUN mkdir -p /app/api/storage && chown -R dify:dify /app/api/storage

# Build argument for commit SHA
ARG COMMIT_SHA
ENV COMMIT_SHA=${COMMIT_SHA}

# Switch to non-root user
USER dify

# Expose port
EXPOSE 5001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5001/health || exit 1

ENTRYPOINT ["/bin/bash", "/entrypoint.sh"]
