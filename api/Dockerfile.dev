# Development Dockerfile for API service
FROM python:3.12-slim-bookworm AS base

WORKDIR /app/api

# Install uv for faster package management
ENV UV_VERSION=0.6.14
RUN pip install --no-cache-dir uv==${UV_VERSION}

# Development stage
FROM base AS development

# Install system dependencies for development
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        # Build tools
        gcc g++ libc-dev libffi-dev libgmp-dev libmpfr-dev libmpc-dev \
        # Development tools
        curl nodejs npm git vim \
        # Runtime dependencies
        libmagic1 media-types \
        # For Security
        expat libldap-2.5-0 perl libsqlite3-0 zlib1g \
    && apt-get autoremove -y \
    && rm -rf /var/lib/apt/lists/*

# Set environment variables for development
ENV FLASK_APP=app.py
ENV FLASK_ENV=development
ENV FLASK_DEBUG=true
ENV EDITION=SELF_HOSTED
ENV DEPLOY_ENV=DEVELOPMENT

# Set timezone
ENV TZ=UTC

# Install Python dependencies
COPY pyproject.toml uv.lock ./
RUN uv sync --locked --dev

# Set up virtual environment
ENV VIRTUAL_ENV=/app/api/.venv
ENV PATH="${VIRTUAL_ENV}/bin:${PATH}"

# Download nltk data
RUN python -c "import nltk; nltk.download('punkt'); nltk.download('averaged_perceptron_tagger')"

# Set up tiktoken cache
ENV TIKTOKEN_CACHE_DIR=/app/api/.tiktoken_cache
RUN python -c "import tiktoken; tiktoken.encoding_for_model('gpt2')"

# Copy source code (this will be overridden by volume mount in development)
COPY . /app/api/

# Copy entrypoint
COPY docker/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

# Expose port
EXPOSE 5001

# Default command for development (can be overridden in docker-compose)
ENTRYPOINT ["/bin/bash", "/entrypoint.sh"]
