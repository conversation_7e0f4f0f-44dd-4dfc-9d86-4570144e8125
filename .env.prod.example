# Production Environment Configuration Template
# Copy this file to .env.prod and fill in the actual values for production deployment

# ------------------------------
# Environment
# ------------------------------
DEPLOY_ENV=PRODUCTION
DEBUG=false
FLASK_DEBUG=false
LOG_LEVEL=INFO

# ------------------------------
# URLs - Production (REQUIRED - Replace with your actual domains)
# ------------------------------
CONSOLE_API_URL=https://api.yourdomain.com
CONSOLE_WEB_URL=https://console.yourdomain.com
SERVICE_API_URL=https://api.yourdomain.com
APP_API_URL=https://api.yourdomain.com
APP_WEB_URL=https://app.yourdomain.com
FILES_URL=https://api.yourdomain.com

# ------------------------------
# Security - Production Keys (REQUIRED - Generate secure keys)
# ------------------------------
# Generate with: openssl rand -base64 42
SECRET_KEY=CHANGE_THIS_TO_A_SECURE_SECRET_KEY

# ------------------------------
# Database Configuration (REQUIRED)
# ------------------------------
DB_USERNAME=postgres
DB_PASSWORD=CHANGE_THIS_TO_A_SECURE_PASSWORD
DB_HOST=db
DB_PORT=5432
DB_DATABASE=dify

# PostgreSQL Performance Settings (Production)
POSTGRES_MAX_CONNECTIONS=200
POSTGRES_SHARED_BUFFERS=512MB
POSTGRES_WORK_MEM=8MB
POSTGRES_MAINTENANCE_WORK_MEM=256MB
POSTGRES_EFFECTIVE_CACHE_SIZE=8GB

# ------------------------------
# Redis Configuration (REQUIRED)
# ------------------------------
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=CHANGE_THIS_TO_A_SECURE_PASSWORD
REDIS_DB=0
REDIS_MAX_MEMORY=1gb

# ------------------------------
# Celery Configuration
# ------------------------------
CELERY_BROKER_URL=redis://:YOUR_REDIS_PASSWORD@redis:6379/1
CELERY_WORKER_AMOUNT=4
CELERY_AUTO_SCALE=true
CELERY_MAX_WORKERS=8
CELERY_MIN_WORKERS=2

# ------------------------------
# Storage Configuration
# ------------------------------
# For production, consider using cloud storage (S3, Azure Blob, etc.)
STORAGE_TYPE=opendal
OPENDAL_SCHEME=fs
OPENDAL_FS_ROOT=storage

# S3 Configuration (if using S3)
# S3_ENDPOINT=
# S3_REGION=us-east-1
# S3_BUCKET_NAME=your-bucket-name
# S3_ACCESS_KEY=
# S3_SECRET_KEY=

# ------------------------------
# Vector Store Configuration (REQUIRED)
# ------------------------------
VECTOR_STORE=weaviate
WEAVIATE_ENDPOINT=http://weaviate:8080
WEAVIATE_API_KEY=CHANGE_THIS_TO_A_SECURE_API_KEY
WEAVIATE_USERS=<EMAIL>

# ------------------------------
# Code Execution Configuration (REQUIRED)
# ------------------------------
CODE_EXECUTION_ENDPOINT=http://sandbox:8194
CODE_EXECUTION_API_KEY=CHANGE_THIS_TO_A_SECURE_API_KEY
SANDBOX_ENABLE_NETWORK=false

# ------------------------------
# CORS Configuration - Restrictive for Production (REQUIRED)
# ------------------------------
WEB_API_CORS_ALLOW_ORIGINS=https://console.yourdomain.com,https://app.yourdomain.com
CONSOLE_CORS_ALLOW_ORIGINS=https://console.yourdomain.com

# ------------------------------
# Performance Settings - Production
# ------------------------------
SERVER_WORKER_AMOUNT=4
SERVER_WORKER_CLASS=gevent
SERVER_WORKER_CONNECTIONS=1000
GUNICORN_TIMEOUT=360
PM2_INSTANCES=4
WORKER_REPLICAS=2

# ------------------------------
# SSL/TLS Configuration
# ------------------------------
NGINX_PORT=80
NGINX_SSL_PORT=443
NGINX_HTTPS_ENABLED=true
NGINX_SSL_CERT_FILENAME=yourdomain.crt
NGINX_SSL_CERT_KEY_FILENAME=yourdomain.key

# ------------------------------
# File Upload Limits
# ------------------------------
UPLOAD_FILE_SIZE_LIMIT=50
UPLOAD_FILE_BATCH_LIMIT=20
UPLOAD_IMAGE_FILE_SIZE_LIMIT=25
UPLOAD_VIDEO_FILE_SIZE_LIMIT=500
UPLOAD_AUDIO_FILE_SIZE_LIMIT=100

# ------------------------------
# Migration Settings
# ------------------------------
MIGRATION_ENABLED=true

# ------------------------------
# Monitoring and Observability (Optional but Recommended)
# ------------------------------
ENABLE_OTEL=false
OTLP_BASE_ENDPOINT=
SENTRY_DSN=
API_SENTRY_DSN=
WEB_SENTRY_DSN=

# ------------------------------
# Mail Configuration (REQUIRED for production features)
# ------------------------------
MAIL_TYPE=smtp
MAIL_DEFAULT_SEND_FROM=<EMAIL>
SMTP_SERVER=smtp.yourdomain.com
SMTP_PORT=587
SMTP_USERNAME=your-smtp-username
SMTP_PASSWORD=your-smtp-password
SMTP_USE_TLS=true

# ------------------------------
# Plugin Configuration
# ------------------------------
PLUGIN_DAEMON_KEY=CHANGE_THIS_TO_A_SECURE_KEY
PLUGIN_DAEMON_URL=http://plugin_daemon:5002
PLUGIN_MAX_PACKAGE_SIZE=52428800

# ------------------------------
# Marketplace Configuration
# ------------------------------
MARKETPLACE_ENABLED=true
MARKETPLACE_API_URL=https://marketplace.dify.ai

# ------------------------------
# Container Registry Configuration (for image building)
# ------------------------------
REGISTRY_URL=your-registry.com
IMAGE_TAG=latest
COMMIT_SHA=

# ------------------------------
# Production Security Settings
# ------------------------------
# Disable debug features
SQLALCHEMY_ECHO=false
CHECK_UPDATE_URL=https://updates.dify.ai
SCARF_NO_ANALYTICS=false

# Rate limiting and security
API_TOOL_DEFAULT_CONNECT_TIMEOUT=10
API_TOOL_DEFAULT_READ_TIMEOUT=60
ACCESS_TOKEN_EXPIRE_MINUTES=60
REFRESH_TOKEN_EXPIRE_DAYS=30

# ------------------------------
# Backup and Maintenance
# ------------------------------
# Configure regular backups for production
# Set up monitoring and alerting
# Configure log rotation
# Set up SSL certificate auto-renewal
