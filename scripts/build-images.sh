#!/bin/bash

# Build script for Dify production images
# This script builds optimized Docker images for production deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
REGISTRY_URL=${REGISTRY_URL:-"localhost"}
IMAGE_TAG=${IMAGE_TAG:-"latest"}
COMMIT_SHA=${COMMIT_SHA:-$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")}
BUILD_ARGS=""
PUSH_IMAGES=${PUSH_IMAGES:-false}
PARALLEL_BUILD=${PARALLEL_BUILD:-true}

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -r, --registry URL     Container registry URL (default: localhost)"
    echo "  -t, --tag TAG          Image tag (default: latest)"
    echo "  -c, --commit SHA       Commit SHA (default: auto-detect)"
    echo "  -p, --push             Push images to registry after building"
    echo "  -s, --sequential       Build images sequentially instead of parallel"
    echo "  -h, --help             Show this help message"
    echo ""
    echo "Environment variables:"
    echo "  REGISTRY_URL           Container registry URL"
    echo "  IMAGE_TAG              Image tag"
    echo "  COMMIT_SHA             Commit SHA"
    echo "  PUSH_IMAGES            Push images (true/false)"
    echo "  PARALLEL_BUILD         Build in parallel (true/false)"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Build with defaults"
    echo "  $0 -r myregistry.com -t v1.0.0 -p   # Build and push to registry"
    echo "  $0 -s                                # Build sequentially"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -r|--registry)
            REGISTRY_URL="$2"
            shift 2
            ;;
        -t|--tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        -c|--commit)
            COMMIT_SHA="$2"
            shift 2
            ;;
        -p|--push)
            PUSH_IMAGES=true
            shift
            ;;
        -s|--sequential)
            PARALLEL_BUILD=false
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate inputs
if [[ -z "$REGISTRY_URL" ]]; then
    print_error "Registry URL is required"
    exit 1
fi

if [[ -z "$IMAGE_TAG" ]]; then
    print_error "Image tag is required"
    exit 1
fi

# Set image names
API_IMAGE="${REGISTRY_URL}/dify-api:${IMAGE_TAG}"
WEB_IMAGE="${REGISTRY_URL}/dify-web:${IMAGE_TAG}"

# Build arguments
BUILD_ARGS="--build-arg COMMIT_SHA=${COMMIT_SHA}"

print_status "Starting Dify production image build..."
print_status "Registry: ${REGISTRY_URL}"
print_status "Tag: ${IMAGE_TAG}"
print_status "Commit SHA: ${COMMIT_SHA}"
print_status "Push images: ${PUSH_IMAGES}"
print_status "Parallel build: ${PARALLEL_BUILD}"
echo ""

# Function to build API image
build_api() {
    print_status "Building API image: ${API_IMAGE}"
    docker build \
        -f api/Dockerfile.prod \
        -t "${API_IMAGE}" \
        ${BUILD_ARGS} \
        --target production \
        ./api
    print_success "API image built successfully"
}

# Function to build Web image
build_web() {
    print_status "Building Web image: ${WEB_IMAGE}"
    docker build \
        -f web/Dockerfile.prod \
        -t "${WEB_IMAGE}" \
        ${BUILD_ARGS} \
        --target production \
        ./web
    print_success "Web image built successfully"
}

# Function to push images
push_images() {
    if [[ "$PUSH_IMAGES" == "true" ]]; then
        print_status "Pushing images to registry..."
        
        print_status "Pushing API image..."
        docker push "${API_IMAGE}"
        print_success "API image pushed successfully"
        
        print_status "Pushing Web image..."
        docker push "${WEB_IMAGE}"
        print_success "Web image pushed successfully"
    fi
}

# Build images
if [[ "$PARALLEL_BUILD" == "true" ]]; then
    print_status "Building images in parallel..."
    build_api &
    API_PID=$!
    build_web &
    WEB_PID=$!
    
    # Wait for both builds to complete
    wait $API_PID
    API_EXIT_CODE=$?
    wait $WEB_PID
    WEB_EXIT_CODE=$?
    
    if [[ $API_EXIT_CODE -ne 0 ]]; then
        print_error "API image build failed"
        exit 1
    fi
    
    if [[ $WEB_EXIT_CODE -ne 0 ]]; then
        print_error "Web image build failed"
        exit 1
    fi
else
    print_status "Building images sequentially..."
    build_api
    build_web
fi

# Push images if requested
push_images

print_success "All images built successfully!"
echo ""
print_status "Built images:"
print_status "  API: ${API_IMAGE}"
print_status "  Web: ${WEB_IMAGE}"

if [[ "$PUSH_IMAGES" == "true" ]]; then
    print_status "Images have been pushed to the registry."
fi

print_status "Build completed successfully!"
