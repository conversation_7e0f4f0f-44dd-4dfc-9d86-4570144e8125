#!/bin/bash

# Development startup script for Dify
# This script helps set up and start the development environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --clean        Clean up existing containers and volumes"
    echo "  --build        Force rebuild of images"
    echo "  --logs         Show logs after starting"
    echo "  -h, --help     Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                    # Start development environment"
    echo "  $0 --clean --build   # Clean rebuild and start"
    echo "  $0 --logs            # Start and show logs"
}

# Default values
CLEAN=false
BUILD=false
SHOW_LOGS=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --clean)
            CLEAN=true
            shift
            ;;
        --build)
            BUILD=true
            shift
            ;;
        --logs)
            SHOW_LOGS=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

print_status "Starting Dify development environment..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker and try again."
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    print_status "Creating .env file from .env.dev template..."
    cp .env.dev .env
    print_success ".env file created. You can customize it if needed."
fi

# Clean up if requested
if [ "$CLEAN" = true ]; then
    print_status "Cleaning up existing containers and volumes..."
    docker compose -f docker-compose.dev.yaml down -v --remove-orphans
    print_success "Cleanup completed."
fi

# Build images if requested
if [ "$BUILD" = true ]; then
    print_status "Building development images..."
    docker compose -f docker-compose.dev.yaml build --no-cache
    print_success "Images built successfully."
fi

# Start the services
print_status "Starting development services..."
docker compose -f docker-compose.dev.yaml up -d

# Wait for services to be ready
print_status "Waiting for services to be ready..."
sleep 10

# Check if services are running
if docker compose -f docker-compose.dev.yaml ps | grep -q "Up"; then
    print_success "Development environment started successfully!"
    echo ""
    print_status "Services available at:"
    print_status "  Web UI:      http://localhost:3000"
    print_status "  API:         http://localhost:5001"
    print_status "  Database:    localhost:5432"
    print_status "  Redis:       localhost:6379"
    print_status "  Weaviate:    http://localhost:8080"
    print_status "  Sandbox:     http://localhost:8194"
    echo ""
    print_status "To view logs: docker compose -f docker-compose.dev.yaml logs -f"
    print_status "To stop:      docker compose -f docker-compose.dev.yaml down"
    
    if [ "$SHOW_LOGS" = true ]; then
        echo ""
        print_status "Showing logs (Ctrl+C to exit)..."
        docker compose -f docker-compose.dev.yaml logs -f
    fi
else
    print_error "Some services failed to start. Check the logs:"
    docker compose -f docker-compose.dev.yaml logs
    exit 1
fi
