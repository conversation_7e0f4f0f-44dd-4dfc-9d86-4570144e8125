# Development Environment Configuration
# This file contains environment variables for local development

# ------------------------------
# Environment
# ------------------------------
DEPLOY_ENV=DEVELOPMENT
DEBUG=true
FLASK_DEBUG=true
LOG_LEVEL=DEBUG

# ------------------------------
# URLs - Local Development
# ------------------------------
CONSOLE_API_URL=http://localhost:5001
CONSOLE_WEB_URL=http://localhost:3000
SERVICE_API_URL=http://localhost:5001
APP_API_URL=http://localhost:5001
APP_WEB_URL=http://localhost:3000
FILES_URL=http://localhost:5001

# ------------------------------
# Security - Development Keys (DO NOT USE IN PRODUCTION)
# ------------------------------
SECRET_KEY=dev-secret-key-change-in-production

# ------------------------------
# Database Configuration
# ------------------------------
DB_USERNAME=postgres
DB_PASSWORD=difyai123456
DB_HOST=db
DB_PORT=5432
DB_DATABASE=dify

# PostgreSQL Performance Settings (Development)
POSTGRES_MAX_CONNECTIONS=50
POSTGRES_SHARED_BUFFERS=128MB
POSTGRES_WORK_MEM=4MB
POSTGRES_MAINTENANCE_WORK_MEM=64MB
POSTGRES_EFFECTIVE_CACHE_SIZE=1GB

# ------------------------------
# Redis Configuration
# ------------------------------
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=difyai123456
REDIS_DB=0
REDIS_MAX_MEMORY=256mb

# ------------------------------
# Celery Configuration
# ------------------------------
CELERY_BROKER_URL=redis://:difyai123456@redis:6379/1
CELERY_WORKER_AMOUNT=1
CELERY_AUTO_SCALE=false

# ------------------------------
# Storage Configuration
# ------------------------------
STORAGE_TYPE=opendal
OPENDAL_SCHEME=fs
OPENDAL_FS_ROOT=storage

# ------------------------------
# Vector Store Configuration
# ------------------------------
VECTOR_STORE=weaviate
WEAVIATE_ENDPOINT=http://weaviate:8080
WEAVIATE_API_KEY=WVF5YThaHlkYwhGUSmCRgsX3tD5ngdN8pkih
WEAVIATE_USERS=<EMAIL>

# ------------------------------
# Code Execution Configuration
# ------------------------------
CODE_EXECUTION_ENDPOINT=http://sandbox:8194
CODE_EXECUTION_API_KEY=dify-sandbox
SANDBOX_ENABLE_NETWORK=true

# ------------------------------
# CORS Configuration - Permissive for Development
# ------------------------------
WEB_API_CORS_ALLOW_ORIGINS=*
CONSOLE_CORS_ALLOW_ORIGINS=*

# ------------------------------
# Performance Settings - Development
# ------------------------------
SERVER_WORKER_AMOUNT=1
SERVER_WORKER_CLASS=gevent
GUNICORN_TIMEOUT=200

# ------------------------------
# File Upload Limits
# ------------------------------
UPLOAD_FILE_SIZE_LIMIT=15
UPLOAD_FILE_BATCH_LIMIT=5
UPLOAD_IMAGE_FILE_SIZE_LIMIT=10
UPLOAD_VIDEO_FILE_SIZE_LIMIT=100
UPLOAD_AUDIO_FILE_SIZE_LIMIT=50

# ------------------------------
# Migration Settings
# ------------------------------
MIGRATION_ENABLED=true

# ------------------------------
# Monitoring and Observability
# ------------------------------
ENABLE_OTEL=false
SENTRY_DSN=
API_SENTRY_DSN=
WEB_SENTRY_DSN=

# ------------------------------
# Mail Configuration (Optional for Development)
# ------------------------------
MAIL_TYPE=
MAIL_DEFAULT_SEND_FROM=
SMTP_SERVER=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
SMTP_USE_TLS=true

# ------------------------------
# Plugin Configuration
# ------------------------------
PLUGIN_DAEMON_KEY=lYkiYYT6owG+71oLerGzA7GXCgOT++6ovaezWAjpCjf+Sjc3ZtU+qUEi
PLUGIN_DAEMON_URL=http://localhost:5002
PLUGIN_MAX_PACKAGE_SIZE=52428800

# ------------------------------
# Marketplace Configuration
# ------------------------------
MARKETPLACE_ENABLED=true
MARKETPLACE_API_URL=https://marketplace.dify.ai

# ------------------------------
# Development Specific Settings
# ------------------------------
# Enable hot reload for development
FLASK_ENV=development
NODE_ENV=development
NEXT_TELEMETRY_DISABLED=1

# Development database settings
SQLALCHEMY_ECHO=true

# Disable some production features for development
CHECK_UPDATE_URL=
SCARF_NO_ANALYTICS=true
