#!/bin/bash

# =============================================================================
# Dify Production Setup Script
# =============================================================================
# This script helps you set up Dify for production deployment

set -e

echo "🚀 Dify Production Setup"
echo "========================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to generate random password (alphanumeric only to avoid sed issues)
generate_password() {
    openssl rand -hex 16
}

# Function to generate API key (alphanumeric only to avoid sed issues)
generate_api_key() {
    openssl rand -hex 32
}

echo -e "${BLUE}Checking prerequisites...${NC}"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker is not installed. Please install Docker first.${NC}"
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker compose &> /dev/null; then
    echo -e "${RED}❌ Docker Compose is not installed. Please install Docker Compose first.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}"

# Check if .env already exists
if [ -f ".env" ]; then
    echo -e "${YELLOW}⚠️  .env file already exists${NC}"
    read -p "Do you want to overwrite it? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Setup cancelled."
        exit 0
    fi
fi

echo -e "${BLUE}Creating production environment file...${NC}"

# Copy the template
cp .env.prod .env

echo -e "${BLUE}Generating secure passwords and keys...${NC}"

# Generate secure passwords
DB_PASSWORD=$(generate_password)
REDIS_PASSWORD=$(generate_password)
SECRET_KEY=$(generate_api_key)
WEAVIATE_API_KEY=$(generate_api_key)
CODE_EXECUTION_API_KEY=$(generate_api_key)
PLUGIN_DAEMON_KEY=$(generate_api_key)

# Replace placeholders in .env file using | as delimiter to avoid issues with special chars
sed -i "s|your-secure-db-password-change-this|$DB_PASSWORD|g" .env
sed -i "s|your-secure-redis-password-change-this|$REDIS_PASSWORD|g" .env
sed -i "s|your-super-secret-key-change-this-in-production|$SECRET_KEY|g" .env
sed -i "s|your-weaviate-api-key-change-this|$WEAVIATE_API_KEY|g" .env
sed -i "s|your-sandbox-api-key-change-this|$CODE_EXECUTION_API_KEY|g" .env
sed -i "s|your-plugin-daemon-api-key-change-this|$PLUGIN_DAEMON_KEY|g" .env

echo -e "${GREEN}✅ Secure passwords and keys generated${NC}"

# Ask for domain configuration
echo -e "${BLUE}Domain Configuration${NC}"
read -p "Enter your domain (e.g., dify.yourdomain.com): " DOMAIN

if [ ! -z "$DOMAIN" ]; then
    sed -i "s|your-domain.com|$DOMAIN|g" .env
    echo -e "${GREEN}✅ Domain configured: $DOMAIN${NC}"
else
    echo -e "${YELLOW}⚠️  Using localhost - remember to update domain in .env for production${NC}"
fi

# Ask for email configuration
echo -e "${BLUE}Email Configuration (Optional)${NC}"
read -p "Enter admin email (e.g., <EMAIL>): " ADMIN_EMAIL

if [ ! -z "$ADMIN_EMAIL" ]; then
    sed -i "s|<EMAIL>|$ADMIN_EMAIL|g" .env
    sed -i "s|<EMAIL>|noreply@${DOMAIN:-localhost}|g" .env
    echo -e "${GREEN}✅ Admin email configured: $ADMIN_EMAIL${NC}"
fi

echo -e "${BLUE}Creating necessary directories...${NC}"

# Create necessary directories
mkdir -p volumes/db/data
mkdir -p volumes/redis/data
mkdir -p volumes/weaviate
mkdir -p volumes/app_storage
mkdir -p nginx/ssl

# Make SSL setup script executable
chmod +x nginx/setup-ssl.sh

echo -e "${GREEN}✅ Directories created${NC}"

echo -e "${BLUE}Setting up SSL certificates...${NC}"
echo "For production, you should:"
echo "1. Obtain SSL certificates for your domain"
echo "2. Place them in ./nginx/ssl/ directory"
echo "3. Update nginx configuration to use HTTPS"

echo -e "${GREEN}🎉 Production setup completed!${NC}"
echo
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Review and customize the .env file"
echo "2. Configure your LLM provider API keys in .env"
echo "3. Set up SSL certificates for production"
echo "4. Start the services:"
echo -e "   ${BLUE}docker compose -f docker-compose.prod.yaml up -d${NC}"
echo
echo -e "${YELLOW}Important files created:${NC}"
echo "- .env (production configuration)"
echo "- Generated secure passwords (saved in .env)"
echo
echo -e "${RED}⚠️  Security Notes:${NC}"
echo "- Keep your .env file secure and never commit it to version control"
echo "- Change default passwords if deploying to production"
echo "- Configure firewall rules for your server"
echo "- Set up regular backups"
echo
echo -e "${GREEN}Happy deploying! 🚀${NC}"
