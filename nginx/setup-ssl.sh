#!/bin/bash

# =============================================================================
# Nginx SSL Setup Script for Dify
# =============================================================================
# This script helps you configure SSL for your Dify deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔒 Dify SSL Configuration Setup${NC}"
echo "=================================="

# Check if we're in the right directory
if [ ! -f "nginx.conf" ]; then
    echo -e "${RED}❌ Please run this script from the nginx/ directory${NC}"
    exit 1
fi

echo -e "${BLUE}Choose SSL configuration:${NC}"
echo "1. HTTP only (development/testing)"
echo "2. HTTPS with SSL certificates (production)"
echo "3. Generate self-signed certificates (testing)"

read -p "Enter your choice (1-3): " choice

case $choice in
    1)
        echo -e "${YELLOW}Setting up HTTP-only configuration...${NC}"
        # nginx.conf is already the HTTP-only version
        echo -e "${GREEN}✅ HTTP configuration is active${NC}"
        echo -e "${YELLOW}⚠️  This configuration is not suitable for production${NC}"
        ;;
    2)
        echo -e "${BLUE}Setting up HTTPS configuration...${NC}"
        
        # Check if SSL certificates exist
        if [ ! -f "ssl/cert.pem" ] || [ ! -f "ssl/key.pem" ]; then
            echo -e "${RED}❌ SSL certificates not found${NC}"
            echo "Please place your SSL certificates in nginx/ssl/ directory:"
            echo "  - nginx/ssl/cert.pem (certificate file)"
            echo "  - nginx/ssl/key.pem (private key file)"
            echo ""
            echo "You can obtain SSL certificates from:"
            echo "  - Let's Encrypt (free): https://letsencrypt.org/"
            echo "  - Your domain provider"
            echo "  - CloudFlare"
            exit 1
        fi
        
        # Backup current config and use SSL version
        cp nginx.conf nginx.conf.backup
        cp nginx-ssl.conf nginx.conf
        
        echo -e "${GREEN}✅ HTTPS configuration is now active${NC}"
        echo -e "${GREEN}✅ HTTP requests will be redirected to HTTPS${NC}"
        ;;
    3)
        echo -e "${YELLOW}Generating self-signed certificates...${NC}"
        
        # Create ssl directory if it doesn't exist
        mkdir -p ssl
        
        # Generate self-signed certificate
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout ssl/key.pem \
            -out ssl/cert.pem \
            -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"
        
        # Use SSL configuration
        cp nginx.conf nginx.conf.backup
        cp nginx-ssl.conf nginx.conf
        
        echo -e "${GREEN}✅ Self-signed certificates generated${NC}"
        echo -e "${GREEN}✅ HTTPS configuration is now active${NC}"
        echo -e "${YELLOW}⚠️  Self-signed certificates will show security warnings in browsers${NC}"
        echo -e "${YELLOW}⚠️  Use real certificates for production${NC}"
        ;;
    *)
        echo -e "${RED}❌ Invalid choice${NC}"
        exit 1
        ;;
esac

echo ""
echo -e "${BLUE}Next steps:${NC}"
echo "1. Restart nginx service:"
echo -e "   ${BLUE}docker-compose -f docker-compose.prod.yaml restart nginx${NC}"
echo "2. Check nginx logs for any errors:"
echo -e "   ${BLUE}docker-compose -f docker-compose.prod.yaml logs nginx${NC}"
echo ""
echo -e "${GREEN}SSL setup completed! 🎉${NC}"
