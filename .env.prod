# =============================================================================
# Dify Production Environment Configuration
# =============================================================================
# Copy this file to .env and configure the values for your production deployment
# Usage: docker-compose -f docker-compose.prod.yaml --env-file .env up -d

# =============================================================================
# BASIC CONFIGURATION
# =============================================================================

# Environment Settings
LOG_LEVEL=INFO
DEBUG=false
FLASK_DEBUG=false
DEPLOY_ENV=PRODUCTION

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# IMPORTANT: Generate a strong secret key for production
# You can generate one using: openssl rand -base64 32
SECRET_KEY=your-super-secret-key-change-this-in-production

# =============================================================================
# URL CONFIGURATION
# =============================================================================
# Replace 'your-domain.com' with your actual domain

# Console URLs (Admin Interface)
CONSOLE_API_URL=https://your-domain.com
CONSOLE_WEB_URL=https://your-domain.com

# App URLs (End User Interface)
APP_API_URL=https://your-domain.com
APP_WEB_URL=https://your-domain.com

# Service URLs
SERVICE_API_URL=https://your-domain.com
FILES_URL=https://your-domain.com

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# Configure allowed origins for CORS (comma-separated)

WEB_API_CORS_ALLOW_ORIGINS=https://your-domain.com
CONSOLE_CORS_ALLOW_ORIGINS=https://your-domain.com

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL Database
DB_USERNAME=postgres
DB_PASSWORD=your-secure-db-password-change-this
DB_HOST=db
DB_PORT=5432
DB_DATABASE=dify

# Database Performance Settings
POSTGRES_MAX_CONNECTIONS=100
POSTGRES_SHARED_BUFFERS=256MB
POSTGRES_WORK_MEM=8MB
POSTGRES_MAINTENANCE_WORK_MEM=128MB
POSTGRES_EFFECTIVE_CACHE_SIZE=8GB

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your-secure-redis-password-change-this
REDIS_DB=0
REDIS_MAX_MEMORY=512mb

# =============================================================================
# CELERY CONFIGURATION
# =============================================================================

CELERY_BROKER_URL=redis://:your-secure-redis-password-change-this@redis:6379/1

# =============================================================================
# VECTOR DATABASE CONFIGURATION
# =============================================================================

# Weaviate Configuration
VECTOR_STORE=weaviate
WEAVIATE_ENDPOINT=http://weaviate:8080
WEAVIATE_API_KEY=your-weaviate-api-key-change-this
WEAVIATE_USERS=<EMAIL>

# =============================================================================
# CODE EXECUTION CONFIGURATION
# =============================================================================

CODE_EXECUTION_ENDPOINT=http://sandbox:8194
CODE_EXECUTION_API_KEY=your-sandbox-api-key-change-this

# =============================================================================
# STORAGE CONFIGURATION
# =============================================================================

STORAGE_TYPE=opendal
OPENDAL_SCHEME=fs
OPENDAL_FS_ROOT=storage

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# API Workers
SERVER_WORKER_AMOUNT=2
SERVER_WORKER_CLASS=gevent

# Celery Workers
CELERY_WORKER_AMOUNT=2
WORKER_REPLICAS=2

# Frontend
PM2_INSTANCES=2

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================

# Docker Registry (if using custom images)
REGISTRY_URL=localhost
IMAGE_TAG=latest
COMMIT_SHA=unknown

# Nginx Ports
NGINX_PORT=80
NGINX_SSL_PORT=443

# =============================================================================
# SANDBOX CONFIGURATION
# =============================================================================

SANDBOX_ENABLE_NETWORK=false

# =============================================================================
# PLUGIN DAEMON CONFIGURATION
# =============================================================================

# Plugin Daemon API Key (generate a secure key)
PLUGIN_DAEMON_KEY=your-plugin-daemon-api-key-change-this

# =============================================================================
# LLM PROVIDER API KEYS
# =============================================================================
# Configure your LLM provider API keys

# OpenAI
OPENAI_API_KEY=your-openai-api-key

# Anthropic
ANTHROPIC_API_KEY=your-anthropic-api-key

# Google
GOOGLE_API_KEY=your-google-api-key

# Azure OpenAI
AZURE_OPENAI_API_KEY=your-azure-openai-api-key
AZURE_OPENAI_ENDPOINT=your-azure-openai-endpoint

# =============================================================================
# EMAIL CONFIGURATION (Optional)
# =============================================================================

# SMTP Settings for email notifications
MAIL_TYPE=smtp
MAIL_DEFAULT_SEND_FROM=<EMAIL>
SMTP_SERVER=your-smtp-server.com
SMTP_PORT=587
SMTP_USERNAME=your-smtp-username
SMTP_PASSWORD=your-smtp-password
SMTP_USE_TLS=true

# =============================================================================
# MONITORING & OBSERVABILITY (Optional)
# =============================================================================

# Sentry for error tracking
SENTRY_DSN=your-sentry-dsn

# =============================================================================
# ENTERPRISE FEATURES (Optional)
# =============================================================================

# SSO Configuration
OAUTH_REDIRECT_PATH=/console/api/oauth/authorize
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# =============================================================================
# ADVANCED CONFIGURATION
# =============================================================================

# Rate Limiting
RATE_LIMIT_ENABLED=true

# File Upload Limits
UPLOAD_FILE_SIZE_LIMIT=15
UPLOAD_FILE_BATCH_LIMIT=5
UPLOAD_IMAGE_FILE_SIZE_LIMIT=10

# Session Configuration
SESSION_PERMANENT=true
SESSION_USE_SIGNER=true

# =============================================================================
# BACKUP CONFIGURATION (Optional)
# =============================================================================

# S3 Backup Configuration
BACKUP_S3_BUCKET=your-backup-bucket
BACKUP_S3_ACCESS_KEY=your-s3-access-key
BACKUP_S3_SECRET_KEY=your-s3-secret-key
BACKUP_S3_REGION=us-east-1
